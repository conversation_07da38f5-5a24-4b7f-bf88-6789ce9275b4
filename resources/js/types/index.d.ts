import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'client' | 'team_member';
    avatar?: string;
    phone?: string;
    company?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface TeamMember {
    id: number;
    user_id: number;
    name: string;
    position: string;
    bio?: string;
    avatar?: string;
    expertise?: string[];
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
    user?: User;
}

export interface Service {
    id: number;
    title: string;
    slug: string;
    description: string;
    detailed_description: string;
    features?: string[];
    price_range?: string;
    category: string;
    is_active: boolean;
    sort_order: number;
    meta_title?: string;
    meta_description?: string;
    created_at: string;
    updated_at: string;
}

export interface Category {
    id: number;
    name: string;
    slug: string;
    description?: string;
    color: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
    blog_posts_count?: number;
}

export interface BlogPost {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    featured_image?: string;
    category_id: number;
    author_id: number;
    status: 'draft' | 'published';
    published_at?: string;
    meta_title?: string;
    meta_description?: string;
    views_count: number;
    created_at: string;
    updated_at: string;
    category?: Category;
    author?: User;
    comments_count?: number;
    reading_time?: number;
}

export interface Comment {
    id: number;
    blog_post_id: number;
    user_id?: number;
    author_name?: string;
    author_email?: string;
    content: string;
    is_approved: boolean;
    parent_id?: number;
    created_at: string;
    updated_at: string;
    user?: User;
    replies?: Comment[];
    blog_post?: BlogPost;
}

export interface Consultation {
    id: number;
    user_id: number;
    service_id: number;
    consultation_date: string;
    duration: number;
    status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
    notes?: string;
    meeting_link?: string;
    payment_status: 'pending' | 'paid' | 'refunded';
    payment_id?: string;
    created_at: string;
    updated_at: string;
    user?: User;
    service?: Service;
    payment?: Payment;
    formatted_duration?: string;
}

export interface Payment {
    id: number;
    user_id: number;
    consultation_id?: number;
    amount: number;
    currency: string;
    payment_method: 'paddle' | 'coinbase';
    payment_gateway_id?: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    gateway_response?: any;
    created_at: string;
    updated_at: string;
    user?: User;
    consultation?: Consultation;
    formatted_amount?: string;
}

export interface AnalyticsEvent {
    id: number;
    user_id?: number;
    session_id: string;
    event_name: string;
    event_parameters?: Record<string, any>;
    page_url: string;
    referrer?: string;
    user_agent?: string;
    ip_address?: string;
    pixel_fired: boolean;
    ga4_fired: boolean;
    conversion_value?: number;
    created_at: string;
    updated_at: string;
    user?: User;
}

export interface TrackingSetting {
    id: number;
    setting_name: string;
    setting_value: Record<string, any>;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

export interface ContactSubmission {
    id: number;
    name: string;
    email: string;
    phone?: string;
    company?: string;
    subject: string;
    message: string;
    status: 'new' | 'read' | 'replied' | 'archived';
    admin_notes?: string;
    ip_address?: string;
    user_agent?: string;
    created_at: string;
    updated_at: string;
}

export interface NewsletterSubscription {
    id: number;
    email: string;
    name?: string;
    status: 'active' | 'unsubscribed' | 'bounced';
    preferences?: Record<string, any>;
    subscribed_at: string;
    unsubscribed_at?: string;
    unsubscribe_token: string;
    source?: string;
    created_at: string;
    updated_at: string;
}

export interface Setting {
    id: number;
    key: string;
    value?: string;
    type: 'string' | 'integer' | 'boolean' | 'json';
    description?: string;
    group: string;
    is_public: boolean;
    created_at: string;
    updated_at: string;
}
